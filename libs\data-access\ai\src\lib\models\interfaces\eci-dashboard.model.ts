/**
 * ECI Dashboard specific models for AI store integration
 */

/**
 * Custodian model for ECI dashboard filters
 */
export interface CustodianModel {
  id: number
  name: string
  email: string
}

/**
 * Subcategory data for document types
 */
export interface SubcategoryData {
  subcategory: string
  count: number
}

/**
 * Document type model with hierarchical structure
 */
export interface DocumentTypeModel {
  category: string
  count: number
  subcategories: SubcategoryData[]
}

/**
 * Table data for focused section display
 */
export interface TableData {
  label: string
  count: number
  percent: number
}

/**
 * File data model for list display
 */
export interface FileDataModel {
  fileName: string
  file_content: string | null
  summary: string
}

/**
 * Word cloud data model - compatible with angular-tag-cloud-module CloudData
 */
export interface WordCloudData {
  text: string
  weight: number
  color?: string
  tooltip?: string
  link?: string
  external?: boolean
}

/**
 * Chart data for relevance visualization
 */
export interface RelevanceChartData {
  values: number[]
  labels: string[]
  colors: string[]
}

/**
 * Bar chart data for inappropriate content
 */
export interface BarChartData {
  categories: string[]
  values: number[]
}

/**
 * Pagination state for file list
 */
export interface PaginationState {
  currentPage: number
  pageSize: number
  totalRecords: number
}

/**
 * Filter options for ECI Dashboard data
 */
export interface EciDashboardFilters {
  custodianIds?: number[]
  dateRange?: { start: Date; end: Date }
  documentTypes?: string[]
}

/**
 * ECA Dashboard Type enum - matches backend ECADashboardType (numeric values)
 */
export enum ECADashboardType {
  Relevance = 0,
  DocumentType = 1,
  Topic_Relevant = 2,
  Topic_NonRelevant = 3,
  Inappropriate_Words = 4,
  WordCloud = 5,
}

/**
 * Sunburst Chart Type enum for component differentiation
 */
export enum SunburstChartType {
  Relevance = 'Relevance',
  DocumentTypes = 'Document Types',
  RelevantDocuments = 'Relevant Documents',
  NotRelevantDocuments = 'Not Relevant Documents',
  Inappropriate_Words = 'Inappropriate Words',
  WordCloud = 'Word Cloud',
}

/**
 * ECA Dashboard Request Model - matches backend ECADashboardRequestModel
 */
export interface ECADashboardRequestModel {
  dashboardType: ECADashboardType
  searchTempTable: string
  selectedFileIds: number[]
  unSelectedFileIds: number[]
  isBatchSelection: boolean
}

export interface ECADocumentRequestModel extends ECADashboardRequestModel {
  pageSize: number
  pageNumber: number
  keyword: string
}

/**
 * ECA Relevance Model - matches backend ECARelevance
 */
export interface ECARelevance {
  docCount: number
  relevanceType: string
}

/**
 * Hierarchical Item Interface - matches backend IHierarchicalItem<T>
 */
export interface HierarchicalItem<T> {
  id: number
  parentId: number
  docCount: number
  percentage: number
  children: T[]
}

/**
 * ECA Document Type Model - matches backend ECADocumentType
 */
export interface ECADocumentType extends HierarchicalItem<ECADocumentType> {
  docTypeId: number
  docTypeName: string
  docCount: number
  percentage: number
  parentDocTypeId: number
  children: ECADocumentType[]
}

/**
 * ECA Document Types Response - matches backend ECADocumentTypesResponse
 */
export interface ECADocumentTypesResponse {
  documentTypes: ECADocumentType[]
}

/**
 * ECA Topic Model - matches backend ECATopic
 */
export interface ECATopic extends HierarchicalItem<ECATopic> {
  topicId: number
  topicName: string
  docCount: number
  percentage: number
  parentTopicId: number
  children: ECATopic[]
}

/**
 * ECA Topics Response - matches backend ECATopicsResponse
 */
export interface ECATopicsResponse {
  topics: ECATopic[]
}

/**
 * Chart-specific drill-down state for individual charts
 */
export interface ChartDrillDownState {
  chartType: SunburstChartType
  isExpanded: boolean
  currentLevel: number
  drillDownHistory: any[]
  currentData: any[]
  tableData: TableData[]
  showDetails: boolean
  selectedNode: any | null
}

/**
 * ECI Dashboard UI state
 */
export interface EciDashboardUiState {
  isFocusedSectionOpened: boolean
  isParentData: boolean
  showDetails: boolean
  showFilterPopup: boolean
  showCustodianFilters: boolean
  activeChartType: SunburstChartType | null
  chartDrillDownStates: Partial<Record<SunburstChartType, ChartDrillDownState>>
}

/**
 * ECI Dashboard data state
 */
export interface EciDashboardDataState {
  custodians: CustodianModel[]
  documentTypes: DocumentTypeModel[]
  fileData: FileDataModel[]
  wordCloudData: WordCloudData[]
  relevanceData: RelevanceChartData | null
  barChartData: BarChartData | null
  tableData: TableData[]
  selectedDocuments: number
  totalDocuments: number
  paginationState: PaginationState
}
