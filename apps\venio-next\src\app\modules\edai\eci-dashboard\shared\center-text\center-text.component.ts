import { Component, inject, input, output } from '@angular/core'
import { Ng<PERSON>lass, NgIf } from '@angular/common'
import { hyperlinkOpenSmIcon, SVGIcon } from '@progress/kendo-svg-icons'
import { KENDO_BUTTONS } from '@progress/kendo-angular-buttons'
import { AiFacade, SunburstChartType } from '@venio/data-access/ai'

@Component({
  selector: 'venio-center-text',
  standalone: true,
  imports: [KENDO_BUTTONS, NgIf, NgClass],
  templateUrl: './center-text.component.html',
  styleUrl: './center-text.component.scss',
})
export class CenterTextComponent {
  public readonly centerText = input.required<SunburstChartType>()

  public readonly showViewDetails = input<boolean>(true)

  public readonly isExpanded = input<boolean>(false) // New input to track if chart is expanded

  public readonly showBackButton = input<boolean>(false) // New input to show back button

  // Output event for back button clicks
  public readonly backButtonClick = output<void>()

  private readonly aiFacade = inject(AiFacade)

  public readonly sunburstChartType = SunburstChartType

  public readonly svgOpenNew: SVGIcon = hyperlinkOpenSmIcon

  public openFocusedSection(): void {
    // Determine chart type based on centerText FIRST
    let chartType: SunburstChartType
    const centerText = this.centerText()
    switch (centerText) {
      case 'Document Types':
        chartType = SunburstChartType.DocumentTypes
        break
      case 'Relevant Documents':
        chartType = SunburstChartType.RelevantDocuments
        break
      case 'Not Relevant Documents':
        chartType = SunburstChartType.NotRelevantDocuments
        break
      case 'Relevance':
        chartType = SunburstChartType.Relevance
        break
    }

    console.log('🔄 Opening focused section for chart type:', chartType)

    // Reset any previous state to ensure clean transition
    this.aiFacade.setEciShowDetails(false)

    // Set the active chart type FIRST before opening focused section
    this.aiFacade.setActiveChartType(chartType)

    // Small delay to ensure state is properly set before opening
    setTimeout(() => {
      this.aiFacade.setEciFocusedSectionOpened(true)
    }, 10)
  }

  public onBackButtonClick(): void {
    this.backButtonClick.emit()
  }
}
