import { ComponentFixture, TestBed } from '@angular/core/testing'
import { provideMockStore } from '@ngrx/store/testing'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { AiFacade } from '@venio/data-access/ai'
import { BehaviorSubject } from 'rxjs'

import { FocusedSunburstComponent } from './focused-sunburst.component'
import {
  Component,
  Input,
  CUSTOM_ELEMENTS_SCHEMA,
  NO_ERRORS_SCHEMA,
} from '@angular/core'

// Mock plotly-plot component
@Component({
  selector: 'venio-plotly-plot',
  template: '<div>Mock Plotly Chart</div>',
  standalone: true,
})
class MockPlotlyComponent {
  @Input() public data: any

  @Input() public layout: any

  @Input() public config: any
}

describe('FocusedSunburstComponent', () => {
  let component: FocusedSunburstComponent
  let fixture: ComponentFixture<FocusedSunburstComponent>

  const mockAiFacade = {
    selectEcaDocumentTypesSuccess$: new BehaviorSubject(null),
    selectEciSortedDocumentTypes$: new BehaviorSubject([]),
    selectEcaTopicsRelevantSuccess$: new BehaviorSubject(null),
    selectEcaTopicsNonRelevantSuccess$: new BehaviorSubject(null),
    selectEcaRelevanceSuccess$: new BehaviorSubject(null),
    selectEciIsParentData$: new BehaviorSubject(true),
    selectChartCurrentData$: jest.fn().mockReturnValue(new BehaviorSubject([])),
    selectChartIsExpanded$: jest
      .fn()
      .mockReturnValue(new BehaviorSubject(false)),
    selectChartSelectedNode$: jest
      .fn()
      .mockReturnValue(new BehaviorSubject(null)),
    selectChartCurrentLevel$: jest.fn().mockReturnValue(new BehaviorSubject(0)),
    selectChartCanGoBack$: jest
      .fn()
      .mockReturnValue(new BehaviorSubject(false)),
    drillDownToNextLevel: jest.fn(),
    drillBackToPreviousLevel: jest.fn(),
    updateChartTableData: jest.fn(),
    setChartSelectedNode: jest.fn(),
    selectChartDrillDownState$: jest
      .fn()
      .mockReturnValue(new BehaviorSubject(null)),
    // Add missing loading state observables
    selectIsEcaDocumentTypesLoading$: new BehaviorSubject(false),
    selectIsEcaTopicsRelevantLoading$: new BehaviorSubject(false),
    selectIsEcaTopicsNonRelevantLoading$: new BehaviorSubject(false),
    selectIsEcaRelevanceLoading$: new BehaviorSubject(false),
    // Add missing show details observable
    selectEciShowDetails$: new BehaviorSubject(false),
  } satisfies Partial<AiFacade>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [FocusedSunburstComponent, MockPlotlyComponent],
      providers: [
        provideMockStore({}),
        provideHttpClient(),
        provideHttpClientTesting(),
        { provide: AiFacade, useValue: mockAiFacade },
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA],
    })
      .overrideComponent(FocusedSunburstComponent, {
        set: {
          imports: [MockPlotlyComponent],
          template: `
            <div class="t-w-full t-h-[300px] md:t-h-[400px] t-relative t-overflow-hidden">
              <venio-plotly-plot [data]="graph.data" [layout]="graph.layout" [config]="config"></venio-plotly-plot>
            </div>
          `,
        },
      })
      .compileComponents()

    fixture = TestBed.createComponent(FocusedSunburstComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
