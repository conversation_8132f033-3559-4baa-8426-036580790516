<div class="t-w-full t-h-[300px] md:t-h-[400px] t-relative t-overflow-hidden">
  <venio-center-text
    [centerText]="chartTitle()"
    [showViewDetails]="false"
    [isExpanded]="chartIsExpanded()"
    [showBackButton]="shouldShowBackButton()"
    (backButtonClick)="resetToInitialState()" />
  @if (shouldRenderChart() && graph()?.data?.length >0) {
  <plotly-plot
    [data]="graph().data"
    [layout]="graph().layout"
    [config]="config()"
    (plotlyClick)="onChartClick($event)"
    (plotlyRelayout)="onChartRelayout($event)"
    (plotlyRestyle)="onChartRestyle($event)" />
  } @else {
  <div class="t-flex t-items-center t-justify-center t-h-full">
    <div class="t-text-gray-500">Loading chart...</div>
  </div>
  }
</div>
