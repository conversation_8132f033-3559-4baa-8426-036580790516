// ViewEncapsulation.None allows direct targeting of Plotly components
:host {
  plotly-plot {
    width: 100% !important;
    height: 100% !important;
    display: block;
    min-height: 300px;
    max-height: 400px;

    // Ensure interactive cursor for chart elements
    .js-plotly-plot .plotly .main-svg {
      cursor: pointer !important;
    }

    // Specific cursor for sunburst segments
    .js-plotly-plot .plotly .main-svg .sunburstlayer path {
      cursor: pointer !important;
    }
  }
}

// Responsive behavior moved to Tailwind classes in template
// Container positioning moved to Tailwind classes in template
