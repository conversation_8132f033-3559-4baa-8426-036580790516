import { ComponentFixture, TestBed } from '@angular/core/testing'
import { provideMockStore } from '@ngrx/store/testing'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideAnimations } from '@angular/platform-browser/animations'
import { AiFacade } from '@venio/data-access/ai'
import { DocumentsFacade, SearchFacade } from '@venio/data-access/review'
import { DialogService } from '@progress/kendo-angular-dialog'
import { BehaviorSubject } from 'rxjs'

import { ListOfFilesComponent } from './list-of-files.component'
import { ActivatedRoute } from '@angular/router'

describe('ListOfFilesComponent', () => {
  let component: ListOfFilesComponent
  let fixture: ComponentFixture<ListOfFilesComponent>

  const mockAiFacade = {
    selectEciFileData$: new BehaviorSubject([]),
    selectEciPagedFileData$: new BehaviorSubject([]),
    selectActiveChartType$: new BehaviorSubject(null),
    selectChartSelectedNode$: jest
      .fn()
      .mockReturnValue(new BehaviorSubject(null)),
    selectEciPaginationState$: new BehaviorSubject({
      currentPage: 1,
      pageSize: 15,
      totalRecords: 0,
    }),
    updateEciPagination: jest.fn(),
  } satisfies Partial<AiFacade>

  const mockDocumentsFacade = {
    loadFulltextViewer: 0,
  } satisfies Partial<DocumentsFacade>

  const mockDialogService = {
    open: jest.fn().mockReturnValue({
      result: {
        subscribe: jest.fn(),
      },
    }),
  } satisfies Partial<DialogService>

  const mockSearchFacade = {
    getSearchResponse$: new BehaviorSubject({
      tempTables: {
        computedSearchTempTable:
          '##tmp_computed_search_f1bcd0afa89e4a4ab1655605f03e25b9_',
        savedSearchTempTable:
          '##tmp_saved_search_f1bcd0afa89e4a4ab1655605f03e25b9_',
        searchResultTempTable:
          '##tmp_search_results_f1bcd0afa89e4a4ab1655605f03e25b9_',
        userTempTable: '',
        viewTypeSearchResultTempTable: null,
        viewTypePagingTempTable: null,
        hitTermResultTempTable: null,
        hitTermTempTable: null,
        searchGuid: 'f1bcd0afa89e4a4ab1655605f03e25b9',
        searchId: 30045,
        baseGUID: 'f1bcd0afa89e4a4ab1655605f03e25b9',
      },
      error: null,
      searchResultIntialParameters: {
        totalHitCount: 0,
      },
    }),
  } satisfies Partial<SearchFacade>

  const mockActivatedRoute = {
    snapshot: {
      queryParams: {
        projectId: 1,
      },
    },
  }

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ListOfFilesComponent],
      providers: [
        provideMockStore({}),
        provideHttpClient(),
        provideHttpClientTesting(),
        provideAnimations(),
        { provide: AiFacade, useValue: mockAiFacade },
        { provide: DocumentsFacade, useValue: mockDocumentsFacade },
        { provide: DialogService, useValue: mockDialogService },
        { provide: SearchFacade, useValue: mockSearchFacade },
        { provide: ActivatedRoute, useValue: mockActivatedRoute },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(ListOfFilesComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
