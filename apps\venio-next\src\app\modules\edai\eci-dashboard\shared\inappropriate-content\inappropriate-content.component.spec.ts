import { ComponentFixture, TestBed } from '@angular/core/testing'
import { provideMockStore } from '@ngrx/store/testing'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { AiFacade, BarChartData } from '@venio/data-access/ai'
import { BehaviorSubject } from 'rxjs'
import {
  Component,
  Input,
  CUSTOM_ELEMENTS_SCHEMA,
  NO_ERRORS_SCHEMA,
} from '@angular/core'

import { InappropriateContentComponent } from './inappropriate-content.component'

// Mock plotly-plot component
@Component({
  selector: 'venio-plotly-plot',
  template: '<div>Mock Plotly Chart</div>',
  standalone: true,
})
class MockPlotlyComponent {
  @Input() public data: any

  @Input() public layout: any

  @Input() public config: any
}

describe('InappropriateContentComponent', () => {
  let component: InappropriateContentComponent
  let fixture: ComponentFixture<InappropriateContentComponent>

  const mockAiFacade = {
    selectEciBarChartData$: new BehaviorSubject<BarChartData>({
      categories: [],
      values: [],
    }),
  } satisfies Partial<AiFacade>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [InappropriateContentComponent, MockPlotlyComponent],
      providers: [
        provideMockStore({}),
        provideHttpClient(),
        provideHttpClientTesting(),
        { provide: AiFacade, useValue: mockAiFacade },
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA],
    })
      .overrideComponent(InappropriateContentComponent, {
        set: {
          imports: [MockPlotlyComponent],
          template: `
            <div class="t-w-full">
              <div class="t-bg-white t-border t-border-[#dcdcdc] t-flex t-gap-6 t-p-7 t-rounded t-flex-col">
                <div class="t-flex t-justify-between t-items-center t-w-full">
                  <h3 class="t-text-lg t-font-semibold t-text-primary">Content Filter</h3>
                </div>
                <div class="t-w-full t-min-h-[300px]">
                  <venio-plotly-plot [data]="graph.data" [layout]="graph.layout" [config]="config"></venio-plotly-plot>
                </div>
              </div>
            </div>
          `,
        },
      })
      .compileComponents()

    fixture = TestBed.createComponent(InappropriateContentComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
