import { Ng<PERSON>or, DecimalPipe } from '@angular/common'
import { Component, inject, OnInit, computed, output } from '@angular/core'
import { formatParentData } from './helpers'
import { toSignal } from '@angular/core/rxjs-interop'
import { AiFacade, SunburstChartType, TableData } from '@venio/data-access/ai'
import { relevanceChartColors, otherChartColors } from '../../constants/colors'
import { animate, style, transition, trigger } from '@angular/animations'

const headers = ['', 'Percent', 'Count']

const noStyle = {
  height: '0',
  marginTop: '0',
  marginBottom: '0',
  paddingTop: '0',
  paddingBottom: '0',
  opacity: 0,
}
export const rowCollapseExpand = trigger('rowCollapseExpand', [
  transition(':enter', [
    style({ ...noStyle }),
    animate(
      '250ms',
      style({
        height: '*',
        marginTop: '*',
        marginBottom: '*',
        paddingTop: '*',
        paddingBottom: '*',
        opacity: 1,
      })
    ),
  ]),
  transition(':leave', [
    animate('200ms cubic-bezier(0.4,0,0.2,1)', style({ ...noStyle })),
  ]),
])

@Component({
  selector: 'venio-data-table-for-focused-section',
  standalone: true,
  imports: [NgFor, DecimalPipe],
  templateUrl: './data-table-for-focused-section.component.html',
  styleUrl: './data-table-for-focused-section.component.scss',
  animations: [rowCollapseExpand],
})
export class DataTableForFocusedSectionComponent implements OnInit {
  // ...existing code...

  public trackById(index: number, item: any): string | number {
    return item.id || item.label
  }

  private readonly aiFacade = inject(AiFacade)

  public readonly headers = headers

  // Output event for legend clicks
  public readonly legendClick = output<{
    legendName: string
    selectedItem: any
  }>()

  // Computed colors based on active chart type
  public readonly chartColors = computed(() => {
    const activeChart = this.activeChartType()
    return activeChart === SunburstChartType.Relevance
      ? relevanceChartColors
      : otherChartColors
  })

  public readonly activeChartType = toSignal(
    this.aiFacade.selectActiveChartType$,
    {
      initialValue: null,
    }
  )

  // Create signals outside reactive context
  public readonly globalTableDataSignal = toSignal(
    this.aiFacade.selectEciTableData$,
    {
      initialValue: [] as TableData[],
    }
  )

  // Chart-specific table data signals (we'll need to handle this differently)
  public readonly documentTypesTableDataSignal = toSignal(
    this.aiFacade.selectChartTableData$(SunburstChartType.DocumentTypes),
    { initialValue: [] as TableData[] }
  )

  public readonly relevantDocumentsTableDataSignal = toSignal(
    this.aiFacade.selectChartTableData$(SunburstChartType.RelevantDocuments),
    { initialValue: [] as TableData[] }
  )

  public readonly notRelevantDocumentsTableDataSignal = toSignal(
    this.aiFacade.selectChartTableData$(SunburstChartType.NotRelevantDocuments),
    { initialValue: [] as TableData[] }
  )

  public readonly relevanceTableDataSignal = toSignal(
    this.aiFacade.selectChartTableData$(SunburstChartType.Relevance),
    { initialValue: [] as TableData[] }
  )

  // Use chart-specific table data when active chart type is available
  public readonly tableData = computed(() => {
    const activeChart = this.activeChartType()
    if (activeChart) {
      switch (activeChart) {
        case SunburstChartType.DocumentTypes:
          return this.documentTypesTableDataSignal()
        case SunburstChartType.RelevantDocuments:
          return this.relevantDocumentsTableDataSignal()
        case SunburstChartType.NotRelevantDocuments:
          return this.notRelevantDocumentsTableDataSignal()
        case SunburstChartType.Relevance:
          return this.relevanceTableDataSignal()
        default:
          return this.globalTableDataSignal()
      }
    }
    // Fallback to global table data
    return this.globalTableDataSignal()
  })

  public readonly isParentData = toSignal(
    this.aiFacade.selectEciIsParentData$,
    { initialValue: true }
  )

  public readonly documentTypes = toSignal(
    this.aiFacade.selectEciDocumentTypes$,
    { initialValue: [] }
  )

  public ngOnInit(): void {
    // Initialize table data with parent data when document types are available
    this.aiFacade.selectEciDocumentTypes$.subscribe((documentTypes) => {
      if (documentTypes.length > 0 && this.tableData().length === 0) {
        const initialData = formatParentData(documentTypes)
        this.aiFacade.storeEciTableData(initialData)
      }
    })
  }

  public getColorForIndex(index: number): string {
    const tableData = this.tableData()
    const item = tableData[index]
    const activeChart = this.activeChartType()

    // For relevance charts, use sequential colors as they don't have parent-child relationships
    if (activeChart === SunburstChartType.Relevance) {
      const colors = this.chartColors()
      return colors[index + 1] || colors[1] || '#1f77b4'
    }

    // For other charts, try to get the actual chart color by matching the data structure
    if (item && item.label) {
      // Get the document types data to find the correct color mapping
      const documentTypes = this.documentTypes()
      if (documentTypes && documentTypes.length > 0) {
        // Find the index of this item in the original document types array
        const originalIndex = documentTypes.findIndex(
          (docType: any) =>
            docType.category === item.label || docType.name === item.label
        )

        if (originalIndex >= 0) {
          // Use the same color generation logic as the chart
          const colors = this.chartColors()
          const availableColors = colors.slice(1) // Skip transparent

          // For parent elements, use the color at the original index
          return (
            availableColors[originalIndex % availableColors.length] || colors[1]
          )
        }
      }
    }

    // Final fallback to sequential colors
    const colors = this.chartColors()
    return colors[index + 1] || colors[1] || '#1f77b4'
  }

  public onLegendItemClick(item: TableData, index: number): void {
    try {
      const activeChart = this.activeChartType()
      if (!activeChart) {
        console.warn('No active chart type available for legend click')
        return
      }

      console.log('Legend item clicked:', item, 'Chart type:', activeChart)

      // Emit the legend click event to parent component
      this.legendClick.emit({
        legendName: item.label,
        selectedItem: item,
      })
    } catch (error) {
      console.error('Error handling legend item click:', error)
    }
  }
}
