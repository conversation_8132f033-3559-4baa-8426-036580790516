@if (shouldShowFileList()) {
<div class="t-relative t-p-4 t-bg-white t-border t-border-[#dcdcdc] t-rounded">
  <div class="t-flex t-justify-end t-mb-4">
    <venio-pagination
      [disabled]="files().length === 0"
      [totalRecords]="totalRecords()"
      [pageSize]="pageArgs().pageSize"
      [currentPage]="pageArgs().pageNumber"
      [showPageJumper]="false"
      [showPageSize]="true"
      [showRowNumberInputBox]="true"
      (pageChanged)="pageChanged($event)"
      (pageSizeChanged)="pageSizeChanged($event)">
    </venio-pagination>
  </div>
  <kendo-grid [data]="pagedFiles()" [height]="300" [pageable]="false">
    <kendo-grid-column field="rowNumber" title="#" [width]="50">
      <ng-template kendoGridCellTemplate let-rowIndex="rowIndex">
        {{ pageArgs().pageSize * (pageArgs().pageNumber - 1) + (rowIndex + 1) }}
      </ng-template>
    </kendo-grid-column>
    <!-- <kendo-grid-checkbox-column [width]="40"> </kendo-grid-checkbox-column> -->
    <kendo-grid-column field="fileName" title="File Name" [width]="200">
      <ng-template kendoGridCellTemplate let-dataItem>
        <span
          class="t-font-medium t-text-[#1ebadc]"
          (click)="loadFulltextViewer(dataItem)"
          >{{ dataItem.fileName }}</span
        >
      </ng-template>
    </kendo-grid-column>
    <kendo-grid-column field="summary" title="Summary">
      <ng-template kendoGridCellTemplate let-dataItem>
        <div class="t-break-words t-whitespace-normal t-text-gray-600">
          {{ dataItem.summary }}
        </div>
      </ng-template>
    </kendo-grid-column>
  </kendo-grid>
</div>
}
