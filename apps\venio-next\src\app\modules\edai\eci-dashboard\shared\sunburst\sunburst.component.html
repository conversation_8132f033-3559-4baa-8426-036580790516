<div
  [ngClass]="{ 't-border t-border-[#dcdcdc] t-p-7': !isFocusedMode() }"
  class="t-bg-white t-flex t-gap-6 t-rounded t-flex-col">
  <div class="t-flex t-justify-between t-items-center">
    <venio-title-and-download
      [title]="chartTitle()"
      [dashboardType]="ecaDashboadType()"></venio-title-and-download>
    @if (isFocusedMode() && chartCanGoBack()) {
    <button
      kendoButton
      fillMode="clear"
      themeColor="secondary"
      size="small"
      (click)="goBack()">
      <kendo-icon name="arrow-left"></kendo-icon>
      Back
    </button>
    }
  </div>
  <div class="t-w-full t-h-[300px] md:t-h-[400px] t-relative t-overflow-hidden">
    <venio-center-text
      [centerText]="chartTitle()"
      [showViewDetails]="!isFocusedMode()"
      [isExpanded]="chartIsExpanded()"></venio-center-text>

    @if (shouldRenderChart()) {
    <plotly-plot
      [data]="graph().data"
      [layout]="graph().layout"
      [config]="config()"
      (plotlyClick)="onChartClick($event)"
      (plotlyRelayout)="onChartRelayout($event)"
      (plotlyRestyle)="onChartRestyle($event)"></plotly-plot>
    } @else {
    <div class="t-flex t-items-center t-justify-center t-h-full">
      <div class="t-text-gray-500">Loading chart...</div>
    </div>
    }
  </div>
  <venio-vertical-legends
    *ngIf="showLegend()"
    [chartType]="chartTitle()"
    [colors]="chartColors()"
    [isFocusedMode]="isFocusedMode()"
    (legendClick)="onLegendClick($event)"></venio-vertical-legends>
  <div class="t-mb-2"></div>
</div>
