import { Component, computed, inject, PLATFORM_ID } from '@angular/core'
import { PlotlyModule } from 'angular-plotly.js'
import { toSignal } from '@angular/core/rxjs-interop'
import { AiFacade, ECADashboardType } from '@venio/data-access/ai'
import { hyperlinkOpenSmIcon, SVGIcon } from '@progress/kendo-svg-icons'
import { isPlatformBrowser } from '@angular/common'
import { TitleAndDownloadComponent } from '../title-and-download/title-and-download.component'

@Component({
  selector: 'venio-inappropriate-content',
  standalone: true,
  imports: [PlotlyModule, TitleAndDownloadComponent],
  templateUrl: './inappropriate-content.component.html',
  styleUrl: './inappropriate-content.component.scss',
})
export class InappropriateContentComponent {
  public dashboardType = ECADashboardType

  private readonly aiFacade = inject(AiFacade)

  private readonly platformId = inject(PLATFORM_ID)

  private readonly isBrowser = isPlatformBrowser(this.platformId)

  public readonly svgOpenNew: SVGIcon = hyperlinkOpenSmIcon

  public readonly barChartData = toSignal(
    this.aiFacade.selectEciBarChartData$,
    { initialValue: null }
  )

  // Signal to determine if chart data is ready for rendering
  public readonly isChartDataReady = computed(() => {
    const data = this.barChartData()
    return (
      data &&
      data.categories &&
      Array.isArray(data.categories) &&
      data.categories.length > 0 &&
      data.values &&
      Array.isArray(data.values) &&
      data.values.length > 0
    )
  })

  // Signal to determine if chart should be rendered
  public readonly shouldRenderChart = computed(() => {
    return this.isBrowser && this.isChartDataReady()
  })

  public openFocusedSection(): void {
    this.aiFacade.setEciFocusedSectionOpened(true)
  }

  public get graph(): any {
    const data = this.barChartData()
    const categories = data?.categories || []
    const values = data?.values || []

    return {
      data: [
        {
          x: categories,
          y: values,
          type: 'bar',
          marker: {
            color: '#6366f1',
          },
        },
      ],
      layout: {
        autosize: true,
        title: '',
        automargin: true,
        margin: { t: 20, r: 20, b: 60, l: 60 },
        showlegend: false,
        xaxis: {
          title: '',
          tickangle: -45,
        },
        yaxis: {
          title: 'Count',
        },
        plot_bgcolor: 'rgba(0,0,0,0)',
        paper_bgcolor: 'rgba(0,0,0,0)',
      },
    }
  }

  public config = {
    responsive: true,
    displayModeBar: false,
    displaylogo: false,
    modeBarButtonsToRemove: [
      'toImage',
      'sendDataToCloud',
      'editInChartStudio',
      'zoom2d',
      'select2d',
      'pan2d',
      'lasso2d',
      'autoScale2d',
      'resetScale2d',
    ],
  }
}
