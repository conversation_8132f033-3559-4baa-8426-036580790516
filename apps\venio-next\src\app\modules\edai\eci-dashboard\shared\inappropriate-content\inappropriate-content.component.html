<div class="t-w-full">
  <div
    id="content-filter-chart-container"
    class="t-bg-white t-border t-border-[#dcdcdc] t-flex t-gap-6 t-p-7 t-rounded t-flex-col">
    <div class="t-flex t-justify-between t-items-center t-w-full">
      <!-- <div
        class="t-flex t-flex-col md:t-flex-row t-items-start md:t-items-center t-gap-0 md:t-gap-6">
        <h3
          class="t-text-lg t-font-semibold t-text-primary t-max-h-[36px] t-overflow-visible t-z-10">
          Content Filter
        </h3>
        <div
          class="t-flex t-items-center t-cursor-pointer t-text-secondary"
          (click)="openFocusedSection()">
          <span class="t-text-xs t-font-medium">View Details</span>
          <button
            kendoButton
            [svgIcon]="svgOpenNew"
            title="Open in new tab"
            fillMode="flat"
            class="t-text-secondary"></button>
        </div>
      </div> -->
      <div class="t-flex t-space-x-2 t-items-center">
        <venio-title-and-download
          [title]="'Content Filter'"
          [dashboardType]="
            dashboardType.Inappropriate_Words
          "></venio-title-and-download>
      </div>
    </div>
    <div class="t-w-full t-min-h-[300px]">
      @if (shouldRenderChart()) {
      <plotly-plot
        id="content-filter-chart"
        [data]="graph.data"
        [layout]="graph.layout"
        [config]="config"></plotly-plot>
      } @else {
      <div class="t-flex t-items-center t-justify-center t-h-[300px]">
        <div class="t-text-gray-500">Loading chart...</div>
      </div>
      }
    </div>
  </div>
</div>
