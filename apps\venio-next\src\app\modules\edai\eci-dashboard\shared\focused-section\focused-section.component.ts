import { Component, inject, viewChild } from '@angular/core'
import { FocusedSunburstComponent } from '../focused-sunburst/focused-sunburst.component'
import { DataTableForFocusedSectionComponent } from '../data-table-for-focused-section/data-table-for-focused-section.component'
import { ListOfFilesComponent } from '../list-of-files/list-of-files.component'
import { NgIf } from '@angular/common'
import { toSignal } from '@angular/core/rxjs-interop'
import { AiFacade } from '@venio/data-access/ai'

@Component({
  selector: 'venio-focused-section',
  standalone: true,
  imports: [
    FocusedSunburstComponent,
    DataTableForFocusedSectionComponent,
    ListOfFilesComponent,
    NgIf,
  ],
  templateUrl: './focused-section.component.html',
  styleUrl: './focused-section.component.scss',
})
export class FocusedSectionComponent {
  private readonly aiFacade = inject(AiFacade)

  // Get reference to the focused sunburst component
  public readonly sunburstComponent = viewChild(FocusedSunburstComponent)

  public readonly showDetails = toSignal(this.aiFacade.selectEciShowDetails$, {
    initialValue: false,
  })

  public readonly activeChartType = toSignal(
    this.aiFacade.selectActiveChartType$,
    {
      initialValue: null,
    }
  )

  public onLegendClick(event: { legendName: string; selectedItem: any }): void {
    try {
      console.log('Legend click received in focused section:', event)

      // Pass the legend click to the sunburst component
      const sunburst = this.sunburstComponent()
      if (sunburst) {
        sunburst.onLegendClick(event)
      } else {
        console.warn('Sunburst component not found')
      }
    } catch (error) {
      console.error('Error handling legend click in focused section:', error)
    }
  }
}
