import {
  Component,
  inject,
  computed,
  OnD<PERSON>roy,
  OnInit,
  ViewContainerRef,
  effect,
  Signal,
  WritableSignal,
  signal,
  runInInjectionContext,
  Injector,
  ɵunwrapWritableSignal,
} from '@angular/core'
import { GridModule } from '@progress/kendo-angular-grid'
import { UiPaginationModule, PageArgs } from '@venio/ui/pagination'
import { toSignal } from '@angular/core/rxjs-interop'
import {
  AiFacade,
  ECADashboardType,
  FileDataModel,
  SunburstChartType,
} from '@venio/data-access/ai'
import {
  DialogRef,
  DialogService,
  DialogsModule,
} from '@progress/kendo-angular-dialog'
import {
  DocumentsFacade,
  SearchFacade,
  Viewer,
} from '@venio/data-access/review'
import { filter, Subject, take, takeUntil } from 'rxjs'
import { ResponseModel } from '@venio/shared/models/interfaces'
import { ActivatedRoute } from '@angular/router'

@Component({
  selector: 'venio-list-of-files',
  standalone: true,
  imports: [GridModule, UiPaginationModule, DialogsModule],
  templateUrl: './list-of-files.component.html',
  styleUrls: ['./list-of-files.component.scss'],
})
export class ListOfFilesComponent implements OnInit, OnDestroy {
  private readonly aiFacade = inject(AiFacade)
  private readonly dialogService = inject(DialogService)
  private readonly documentsFacade = inject(DocumentsFacade)
  private readonly searchFacade = inject(SearchFacade)
  private readonly viewContainerRef = inject(ViewContainerRef)
  private readonly activatedRoute = inject(ActivatedRoute)
  private readonly injector = inject(Injector)

  private get projectId(): number {
    return Number(this.activatedRoute.snapshot.queryParams['projectId'])
  }

  public pageArgs: WritableSignal<PageArgs> = signal({
    pageSize: 15,
    pageNumber: 1,
  })
  public totalRecords: WritableSignal<number> = signal(0)
  private readonly searchResponse = toSignal(
    this.searchFacade.getSearchResponse$
  )
  private readonly selectedDocuments = toSignal(
    this.documentsFacade.getSelectedDocuments$
  )
  private readonly unSelectedDocuments = toSignal(
    this.documentsFacade.getUnselectedDocuments$
  )
  private readonly isBatchSelected = toSignal(
    this.documentsFacade.getIsBatchSelected$
  )

  private toDestroy$ = new Subject<void>()

  public readonly files = toSignal(this.aiFacade.selectEciFileData$, {
    initialValue: [],
  })

  public pagedFiles: WritableSignal<FileDataModel[]> = signal([])

  public readonly paginationState = toSignal(
    this.aiFacade.selectEciPaginationState$,
    { initialValue: { currentPage: 1, pageSize: 15, totalRecords: 0 } }
  )
  public currentFileId: number

  // Signal selectors for active chart type and selected node data
  public readonly activeChartType = toSignal(
    this.aiFacade.selectActiveChartType$,
    { initialValue: null }
  )

  // Chart-specific selected node signals
  public readonly documentTypesSelectedNode = toSignal(
    this.aiFacade.selectChartSelectedNode$(SunburstChartType.DocumentTypes),
    { initialValue: null }
  )

  public readonly relevantDocumentsSelectedNode = toSignal(
    this.aiFacade.selectChartSelectedNode$(SunburstChartType.RelevantDocuments),
    { initialValue: null }
  )

  public readonly notRelevantDocumentsSelectedNode = toSignal(
    this.aiFacade.selectChartSelectedNode$(
      SunburstChartType.NotRelevantDocuments
    ),
    { initialValue: null }
  )

  public readonly relevanceSelectedNode = toSignal(
    this.aiFacade.selectChartSelectedNode$(SunburstChartType.Relevance),
    { initialValue: null }
  )

  // Computed signal for selected node data based on active chart type
  public readonly selectedNodeData = computed(() => {
    const chartType = this.activeChartType()
    if (chartType) {
      let selectedNode = null
      let dashboardType = null
      switch (chartType) {
        case SunburstChartType.DocumentTypes:
          selectedNode = this.documentTypesSelectedNode()
          dashboardType = ECADashboardType.DocumentType
          break
        case SunburstChartType.RelevantDocuments:
          selectedNode = this.relevantDocumentsSelectedNode()
          dashboardType = ECADashboardType.Topic_Relevant
          break
        case SunburstChartType.NotRelevantDocuments:
          selectedNode = this.notRelevantDocumentsSelectedNode()
          dashboardType = ECADashboardType.Topic_NonRelevant
          break
        case SunburstChartType.Relevance:
          selectedNode = this.relevanceSelectedNode()
          dashboardType = ECADashboardType.Relevance
          break
        // case SunburstChartType.Inappropriate_Words:
        //   selectedNode = this.inappropriateWordsSelectedNode()
        //   dashboardType = ECADashboardType.Inappropriate_Words
        //   break
        // case SunburstChartType.WordCloud:
        //   selectedNode = this.wordCloudSelectedNode()
        //   dashboardType = ECADashboardType.WordCloud
        //   break
      }

      console.log('List of Files - Active Chart Type:', chartType)
      console.log('List of Files - Selected Node Data:', selectedNode)

      return { selectedNode, dashboardType }
    }
    return null
  })

  // Computed signal to determine if file list should be visible
  public readonly shouldShowFileList = computed(() => {
    const selectedNode = this.selectedNodeData()
    const hasSelection = selectedNode !== null && selectedNode !== undefined

    console.log('List of Files - Should Show:', hasSelection)

    return hasSelection
  })

  public loadDocumentListEffect(): void {
    let selectedKeyword = ''
    let selectedChartType = ''
    runInInjectionContext(this.injector, () =>
      effect(
        () => {
          const newKeyWord =
            this.selectedNodeData().selectedNode?.category ??
            this.selectedNodeData().selectedNode?.subcategory

          if (
            selectedKeyword !== newKeyWord ||
            selectedChartType !== this.activeChartType()
          ) {
            this.pageArgs.set({ pageSize: 15, pageNumber: 1 })
          }
          selectedKeyword = newKeyWord
          selectedChartType = this.activeChartType()
          const pageArgs = this.pageArgs()

          if (selectedKeyword && selectedChartType) {
            this.aiFacade
              .loadEcaDocumentList(this.projectId, {
                dashboardType: this.selectedNodeData().dashboardType,
                searchTempTable:
                  this.searchResponse().tempTables.searchResultTempTable,
                selectedFileIds: this.selectedDocuments(),
                unSelectedFileIds: this.unSelectedDocuments(),
                isBatchSelection: this.isBatchSelected(),
                keyword: newKeyWord,
                pageNumber: pageArgs.pageNumber,
                pageSize: pageArgs.pageSize,
              })
              .pipe(take(1), takeUntil(this.toDestroy$))
              .subscribe((response: ResponseModel) => {
                this.totalRecords.set(response.data?.totalCount || 0)
                this.pagedFiles.set(response.data?.documents as FileDataModel[])
              })
          }
        },
        { allowSignalWrites: true }
      )
    )
  }

  public pageChanged(event: PageArgs): void {
    const currentPageArgs = { ...this.pageArgs(), pageNumber: event.pageNumber }
    this.pageArgs.set(currentPageArgs)
  }

  public pageSizeChanged(event: PageArgs): void {
    const currentPageArgs = { ...this.pageArgs(), pageSize: event.pageSize }
    this.pageArgs.set(currentPageArgs)
  }

  public ngOnInit(): void {
    this.loadDocumentListEffect()
    this.registerViewerReady()
  }
  private registerViewerReady() {
    this.documentsFacade.onViewerComponentReady
      .pipe(
        filter(() => this.currentFileId > 0),
        takeUntil(this.toDestroy$)
      )
      .subscribe((viewer: Viewer) => {
        if (viewer === Viewer.Fulltext) {
          this.documentsFacade.loadFulltextViewer = this.currentFileId
        }
      })
  }

  public loadFulltextViewer(dataItem: any): void {
    this.currentFileId = dataItem?.fileId ?? 2
    if (this.currentFileId > 0)
      this.#handleLazyLoadedFulltextViewer(this.currentFileId)
  }

  #handleLazyLoadedFulltextViewer(fileId: number): void {
    import('@venio/feature/fulltext-viewer')
      .then((d) => {
        this.#launchFulltextViewerDialog(d.FeatureFulltextViewerComponent)
      })
      .catch((error) => {
        console.error('Failed to load fulltext viewer:', error)
      })
  }

  #launchFulltextViewerDialog(dialogContent: any): void {
    const dialogRef: DialogRef = this.dialogService.open({
      appendTo: this.viewContainerRef,
      content: dialogContent,
      width: '95vw',
      height: '95vh',
      title: 'Fulltext Viewer',
    })

    dialogRef.result.pipe(take(1)).subscribe(() => {
      this.documentsFacade.viewerComponentReady = null
      this.currentFileId = 0
    })
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }
}
