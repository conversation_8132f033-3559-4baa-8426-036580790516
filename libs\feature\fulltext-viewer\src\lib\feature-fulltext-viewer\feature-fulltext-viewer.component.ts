import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  inject,
  OnDestroy,
  OnInit,
  ViewChild,
  ViewContainerRef,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  EMPTY,
  Subject,
  catchError,
  debounceTime,
  filter,
  from,
  switchMap,
  take,
  takeUntil,
  tap,
} from 'rxjs'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import {
  SvgLoaderDirective,
  UserGroupRightCheckDirective,
} from '@venio/feature/shared/directives'
import { LoaderModule } from '@progress/kendo-angular-indicators'
import { InfiniteScrollModule } from 'ngx-infinite-scroll'
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms'

import {
  ChunkDocumentPartModel,
  ResponseModel,
} from '@venio/shared/models/interfaces'
import { HttpErrorResponse } from '@angular/common/http'

import {
  ChunkDocumentModel,
  DocumentsFacade,
  Field,
  FieldFacade,
  FieldSelectorViewModel,
  HighlightNavigationAction,
  IndexedDBHandlerService,
  IndexedDBViewerType,
  InitialSearchResultParameter,
  ReviewParamService,
  SearchDocumentMetadata,
  SearchFacade,
  SearchFieldViewerType,
  SearchResultRequestData,
  TempTableResponseModel,
  UserRights,
  Viewer,
  ActionModel,
  FulltextAction,
  HighlightType,
  FieldSelector,
  TextSettings,
  HighlightGroupSettingInfo,
  FulltextFacade,
  FieldSelectionDialogResult,
  StartupsFacade,
} from '@venio/data-access/review'
import { HighlightNavigatorDirective } from '../directives/highlight-navigator.directive'
import { TextTermHighlighterDirective } from '../directives/text-term-highlighter.directive'

import { PopupModule, Collision } from '@progress/kendo-angular-popup'
import { TextSearchComponent } from './text-search/text-search.component'
import { TooltipsModule } from '@progress/kendo-angular-tooltip'
import {
  DialogRef,
  DialogService,
  DialogsModule,
} from '@progress/kendo-angular-dialog'
import { orderBy } from 'lodash'
import {
  ReviewPanelFacade,
  UtilityPanelFacade,
} from '@venio/data-access/document-utility'
import { VenioNotificationService } from '@venio/feature/notification'
import { ShortcutManager } from '@venio/util/utilities'
import {
  ShortcutKeyBindings,
  UtilityPanelTitle,
} from '@venio/shared/models/constants'
import { toSignal } from '@angular/core/rxjs-interop'

@Component({
  selector: 'lib-feature-fulltext-viewer',
  standalone: true,
  imports: [
    CommonModule,
    ButtonsModule,
    InputsModule,
    LabelModule,
    FormsModule,
    ReactiveFormsModule,
    SvgLoaderDirective,
    InfiniteScrollModule,
    LoaderModule,
    HighlightNavigatorDirective,
    TextTermHighlighterDirective,
    TextSearchComponent,
    PopupModule,
    TooltipsModule,
    DialogsModule,
    UserGroupRightCheckDirective,
  ],
  templateUrl: './feature-fulltext-viewer.component.html',
  styleUrl: './feature-fulltext-viewer.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FeatureFulltextViewerComponent
  implements OnInit, AfterViewInit, OnDestroy
{
  public currentFileId: number

  public projectId: number

  private toDestroy$: Subject<void> = new Subject<void>()

  @ViewChild('container')
  private container: ElementRef<HTMLElement>

  public showSpinner: boolean

  public errorMessage: string

  public fulltextTypeForm: FormGroup

  public selectedFulltextType: number

  @ViewChild('placeholder', { read: ViewContainerRef })
  private fulltextPlaceholderContainer: ViewContainerRef

  private searchParameters: InitialSearchResultParameter

  public showGeneratedTiffOcr: boolean

  public showRedactedTiffOcr: boolean

  public generatedOCRStatus: number

  public redactedOCRStatus: number

  private searchTempTables: TempTableResponseModel

  public selectedMetadata: SearchDocumentMetadata[] = []

  private selectedTextFields: FieldSelectorViewModel[] = []

  private fulltextFields: FieldSelectorViewModel[] = []

  private selectedHighlightGroups: any[] = []

  public showNavigationButton: boolean

  @ViewChild(HighlightNavigatorDirective)
  private highlightNavigator: HighlightNavigatorDirective

  @ViewChild(TextTermHighlighterDirective)
  private textTermHighlighter: TextTermHighlighterDirective

  public collision: Collision = { horizontal: 'flip', vertical: 'fit' }

  public textTermHighlighterProperties = {
    showTextSearch: false,
    textSearchTerm: '',
  }

  public UserRights = UserRights

  @ViewChild('highlightGroupComponent', { read: ViewContainerRef })
  private highlightGroupVCR: ViewContainerRef

  public showHighlightGroup: boolean

  public highlightGroupSetting: HighlightGroupSettingInfo

  private shortcutManager: ShortcutManager

  private startupFacade: StartupsFacade = inject(StartupsFacade)

  public allowViewerTextCopy = toSignal(
    this.startupFacade.hasGroupRight$(UserRights.ALLOW_TO_COPY_TEXT_FROM_VIEWER)
  )

  constructor(
    private fb: FormBuilder,
    private cdr: ChangeDetectorRef,
    private indexDb: IndexedDBHandlerService,
    private fulltextFacade: FulltextFacade,
    private documentFacade: DocumentsFacade,
    private reviewParamService: ReviewParamService,
    private searchFacade: SearchFacade,
    private fieldFacade: FieldFacade,
    private reviewPanelFacade: ReviewPanelFacade,
    private dialogService: DialogService,
    private utilityPanelFacade: UtilityPanelFacade,
    private notificationService: VenioNotificationService
  ) {}

  public ngAfterViewInit(): void {
    this.documentFacade.viewerComponentReady = Viewer.Fulltext
  }

  public ngOnInit(): void {
    this.fieldFacade.getPermittedFields$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((fields: Field[]) => {
        this.cdr.markForCheck()
        this.fulltextFields = fields.map((f) => ({
          displayFieldName: f.displayFieldName,
          fieldDisplayOrder: -1,
          isCustomField: f.isCustomField,
          venioFieldId: f.venioFieldId,
        }))
      })
    this.initForm()
    this.getProjectId()
    this.getSearchInitialParameters()
    this.onHighlightGroupSelection()
    this.fetchTextMetadata()
    this.handleDocumentChange()
    this.handleFulltextTypeValueChange()
    this.onTextSearchAction()
    this.highlightSimilarTerms()
    this.initializeShortcuts()
  }

  private initForm(): void {
    this.fulltextTypeForm = this.fb.group({
      fulltextType: [],
    })
  }

  private initializeShortcuts(): void {
    this.shortcutManager = new ShortcutManager(this.container?.nativeElement)
    this.shortcutManager?.bind(
      [
        ShortcutKeyBindings.NAVIGATE_PREVIOUS_HIGHLIGHT,
        ShortcutKeyBindings.NAVIGATE_NEXT_HIGHLIGHT,
      ],
      this.handleHighlightNavigation
    )
  }

  private getProjectId(): void {
    this.reviewParamService.projectId
      .pipe(take(1), takeUntil(this.toDestroy$))
      .subscribe((projectId: number) => {
        this.projectId = projectId
        this.fieldFacade.fetchAllPermittedFields(this.projectId)
      })
  }

  private getSearchInitialParameters(): void {
    this.searchFacade.getSearchInitialParameters$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((res) => {
        this.searchParameters = res
      })
    this.searchFacade.getSearchTempTables$
      .pipe(
        filter((res) => !!res),
        takeUntil(this.toDestroy$)
      )
      .subscribe((res) => {
        this.searchTempTables = res
      })
  }

  private handleFulltextTypeValueChange(): void {
    this.fulltextTypeForm.valueChanges
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((value) => {
        this.selectedFulltextType = +value.fulltextType
        this.fetchFulltext()
      })
  }

  private fetchFulltext(isRetry = false): void {
    this.clearViewer()
    if (this.selectedFulltextType <= 1) this.fetchExtractedFulltext(isRetry)
    else {
      this.fetchOCRFulltext()
    }
  }

  private fetchOCRFulltext(): void {
    this.getFulltextChunksFromAPI().then((chunkParts) => {
      this.container.nativeElement.insertAdjacentHTML(
        'beforeend',
        chunkParts[0].Content
      )
      if (this.textTermHighlighterProperties.showTextSearch)
        this.textTermHighlighter.highlightSearchTerm(
          this.textTermHighlighterProperties.textSearchTerm
        )
    })
  }

  private handleDocumentChange(): void {
    this.documentFacade.onLoadFulltextViewer
      .pipe(
        debounceTime(200),
        filter((fileId) => fileId > 0),
        tap((fileId: number) => {
          this.currentFileId = fileId
        }),
        switchMap((fileId) => {
          return this.fulltextFacade.fetchFulltextSettings(
            this.projectId,
            fileId
          )
        }),
        tap((response: ResponseModel) => {
          const viewerSettings: TextSettings = response.data
          this.showGeneratedTiffOcr = viewerSettings.showGeneratedTiffOCR
          this.showRedactedTiffOcr = viewerSettings.showRedactedTiffOCR
          this.generatedOCRStatus = viewerSettings.generatedOCRStatus
          this.redactedOCRStatus = viewerSettings.redactedOCRStatus
          this.highlightGroupSetting = viewerSettings.highlightGroupSetting
        }),
        switchMap(() => this.fulltextFacade.highlightGroupActionHandler),
        takeUntil(this.toDestroy$)
      )
      .subscribe((action: ActionModel) => {
        this.cdr.markForCheck()
        this.selectedHighlightGroups = action?.data
          ?.selectedHighlightGroups ?? [
          { itemKey: this.highlightGroupSetting.defaultHighlightGroupId },
        ]
        this.fulltextTypeForm.patchValue({ fulltextType: '1' })
        if (this.selectedTextFields?.length > 0)
          this.fulltextFacade.fetchMetadata$.next(this.selectedTextFields)
      })
  }

  private getPayloadForFulltext(isRetry = false): ChunkDocumentModel {
    //const currentPartCount: number = this.docViewerState?.currentPart ?? 0

    const payload: ChunkDocumentModel = {
      fileId: this.currentFileId,
      userId: +localStorage.UserId,
      projectId: this.projectId,
      chunkPartsCount: 0,
      currentPartIndex: 1,
      isCacheAvailable: false,
      previousFileId: 0,
      searchHighlightList: this.searchParameters?.searchHighlightList,
      type: this.selectedFulltextType,
      conversationId: '',
      highlightTermGroupIds: this.getHighlightGroupForFulltextPaylod(),
      requestPartSize: 100,
      imageSetId: -1,
      isRetry: isRetry,
      includeHeader: false,
    }
    return payload
  }

  private getHighlightGroupForFulltextPaylod(): number[] {
    if (!this.highlightGroupSetting?.isHighlightGroupEnabled) return []
    if (this.selectedHighlightGroups?.length > 0)
      return this.selectedHighlightGroups?.map((item) => item.itemKey)
  }

  public clearViewer(): void {
    this.errorMessage = ''
    if (this.highlightNavigator) this.highlightNavigator.reset()
    this.fulltextPlaceholderContainer?.clear()
    if (this.container?.nativeElement)
      this.container.nativeElement.innerHTML = ''
  }

  public async fetchExtractedFulltext(
    isRetry = false,
    partIndex = 1
  ): Promise<void> {
    this.cdr.markForCheck()
    if (!this.currentFileId || !this.projectId) return
    //this.clearViewer()
    this.showSpinner = true
    const isfullTextFetched = await this.isFulltextChunkFetched(partIndex)
    if (!isfullTextFetched || isRetry) {
      const chunkParts = await this.getFulltextChunksFromAPI(isRetry)
      this.addOrUpdateIndexDB(partIndex, chunkParts)
    } else this.loadFulltext()
  }

  private isFulltextChunkFetched(partIndex: number): Promise<boolean> {
    return this.indexDb.ifSpecificPartExists(
      IndexedDBViewerType.Text,
      this.projectId,
      this.currentFileId,
      partIndex
    )
  }

  private getFulltextChunksFromAPI(
    isRetry = false
  ): Promise<ChunkDocumentPartModel[]> {
    const payload: ChunkDocumentModel = this.getPayloadForFulltext(isRetry)
    return new Promise((resolve, reject) => {
      this.fulltextFacade
        .fetchFulltext<ResponseModel>(
          this.projectId,
          this.currentFileId,
          payload
        )
        .pipe(
          catchError((err: unknown) => {
            this.cdr.markForCheck()
            this.errorMessage = (err as HttpErrorResponse)?.error?.message
            this.showSpinner = false
            return EMPTY
            //return of((err as HttpErrorResponse)?.error)
          }),
          take(1)
        )
        .subscribe((result: ResponseModel) => {
          this.cdr.markForCheck()
          this.errorMessage = ''
          const nearNativeResponse: string = result.data
          const resultInfo = nearNativeResponse.split('╓')
          try {
            const chunkDocument = JSON.parse(resultInfo[0])
            resolve(chunkDocument?.ChunkParts)
          } catch (err) {
            if (this.textTermHighlighterProperties.showTextSearch)
              this.textTermHighlighter.emitHighlightData({
                activeElementIndex: -1,
                totalHighlightCount: 0,
              })
            this.showSpinner = false
            this.errorMessage = resultInfo[0]
            reject('invalid json')
          }
          //const { ChunkPartsCount, TotalPartsCount } = chunkDocument
        })
    })
  }

  private addOrUpdateIndexDB(partIndex, chunkParts): void {
    const fulltextParts = chunkParts.map((part) => {
      return {
        PartId: part.Id,
        FileId: +this.currentFileId,
        PartIndex: part.Index,
        Content: part.Content,
        Size: part.Size,
        TotalParts: part.TotalParts,
        SnippetText: part.SnippetText,
      }
    })
    this.indexDb.addParts(IndexedDBViewerType.Text, fulltextParts)
    this.loadFulltext(partIndex)
  }

  private loadFulltext(partIndex = 1): void {
    this.indexDb
      .getParts(
        IndexedDBViewerType.Text,
        this.projectId,
        this.currentFileId,
        partIndex
      )
      .then((chunks: ChunkDocumentPartModel[]) => {
        this.cdr.markForCheck()
        if (partIndex === 1) this.clearViewer()
        const newChunks = chunks.filter((chunk) => chunk.PartIndex >= partIndex)

        newChunks.forEach((item) => {
          this.container.nativeElement.insertAdjacentHTML(
            'beforeend',
            item.Content
          )
        })
        this.showSpinner = false
        if (this.textTermHighlighterProperties.showTextSearch)
          this.textTermHighlighter.highlightSearchTerm(
            this.textTermHighlighterProperties.textSearchTerm
          )
        this.fulltextFacade.activateSimilarTermsHighlight$.next()
        this.showNavigationButton =
          this.container.nativeElement.querySelectorAll('mark').length > 0
      })
  }

  public onRefresh(): void {
    if (this.selectedTextFields?.length !== 0)
      this.fulltextFacade.fetchMetadata$.next(this.selectedTextFields)
    this.fetchFulltext(true)
  }

  public refreshViewer(): void {
    this.documentFacade.loadFulltextViewer = this.currentFileId
  }

  public onActionClick(action: string): void {
    switch (action) {
      case 'refresh':
        this.onRefresh()
        break
      case 'download':
        this.downloadFulltextFile()
        break
      case 'highlight-next':
        this.highlightNavigator.navigateTerms(HighlightNavigationAction.Next)
        break
      case 'highlight-prev':
        this.highlightNavigator.navigateTerms(
          HighlightNavigationAction.Previous
        )
        break
      case 'highlight-first':
        this.highlightNavigator.navigateTerms(HighlightNavigationAction.First)
        break
      case 'highlight-last':
        this.highlightNavigator.navigateTerms(HighlightNavigationAction.Last)
        break
      case 'search-term':
        //this.textTermHighlighter.highlightSearchTerm('export')
        this.textTermHighlighterProperties.showTextSearch =
          !this.textTermHighlighterProperties.showTextSearch
        break
      case 'show-fields':
        this.showFields()
        break
      case 'highlight-group':
        this.showHighlightGroups()
        break
      default:
        break
    }
  }

  private handleHighlightNavigation = (
    event: KeyboardEvent,
    combo?: string
  ): void => {
    const actions = {
      [ShortcutKeyBindings.NAVIGATE_PREVIOUS_HIGHLIGHT]: (): void => {
        this.onActionClick('highlight-prev')
      },
      [ShortcutKeyBindings.NAVIGATE_NEXT_HIGHLIGHT]: (): void => {
        this.onActionClick('highlight-next')
      },
    }

    // Check if the combo is one of the defined shortcuts
    if (actions[combo]) {
      event.preventDefault()
      event.stopPropagation()
      actions[combo]() // Execute the corresponding action
    }
  }

  private async showHighlightGroups(): Promise<void> {
    this.showHighlightGroup = true
    this.highlightGroupVCR.clear()
    //if (!this.isManageReasonVisible) return
    const { HighlightGroupComponent } = await import(
      './highlight-group/highlight-group.component'
    )
    const highlightGroupComponentRef = this.highlightGroupVCR.createComponent(
      HighlightGroupComponent
    )
    highlightGroupComponentRef.instance.projectId = this.projectId
    highlightGroupComponentRef.instance.selectedHighlightGroups =
      this.selectedHighlightGroups

    this.cdr.markForCheck()
  }

  public onHighlightGroupSelection(): void {
    this.fulltextFacade.highlightGroupActionHandler
      .pipe(
        filter((action) => !!action),
        takeUntil(this.toDestroy$)
      )
      .subscribe((action: ActionModel) => {
        this.cdr.markForCheck()
        this.showHighlightGroup = false
        this.selectedHighlightGroups = action?.data
          ?.selectedHighlightGroups ?? [
          { itemKey: this.highlightGroupSetting.defaultHighlightGroupId },
        ]
        if (action?.data?.action) {
          this.indexDb.clearDB(IndexedDBViewerType.Text)
          this.fetchFulltext(true)
        }
      })
  }

  private showFields(): void {
    import('./field-selector/field-selector.component').then((d) => {
      const dialog: DialogRef = this.dialogService.open({
        content: d.FieldSelectorComponent,
        width: '62%',
        height: '100%',
        maxWidth: '900px',
        minWidth: '400px',
        minHeight: '400px',
        maxHeight: '500px',
      })
      const fieldSelectorComponent = dialog.content.instance as FieldSelector
      fieldSelectorComponent.availableFields = this.fulltextFields
      fieldSelectorComponent.visibleFields = this.selectedTextFields

      dialog.result
        .pipe(take(1), takeUntil(this.toDestroy$))
        .subscribe((result: FieldSelectionDialogResult) => {
          this.cdr.markForCheck()
          if (result.action === 'save') {
            this.selectedTextFields = result.data || []
            this.fulltextFacade.fetchMetadata$.next(result.data)
          }
        })
    })
  }

  private fetchTextMetadata(): void {
    this.fulltextFacade.fetchMetadata$
      .pipe(
        tap((fields: FieldSelectorViewModel[]) => {
          this.selectedTextFields = fields || []
        }),
        filter(() => !!this.currentFileId),
        switchMap(() => {
          return this.fulltextFacade.fetchTextMetadata(
            this.projectId,
            this.currentFileId,
            this.getTextMetaDataPayload()
          )
        }),
        takeUntil(this.toDestroy$)
      )
      .subscribe((res: ResponseModel) => {
        this.cdr.markForCheck()
        this.selectedMetadata = res.data.filter(
          (f) =>
            f.key !== 'FileID' && f.key !== 'seq_no' && !f.key.startsWith('__')
        )
        this.selectedMetadata = orderBy(
          this.selectedMetadata.map((f) => ({
            ...f,
            order: this.selectedTextFields.find(
              (field) => field.displayFieldName === f.key
            )?.fieldDisplayOrder,
          })),
          'order'
        )
      })
  }

  private getTextMetaDataPayload(): SearchResultRequestData {
    // Extract non-custom fields and map them to their venioFieldIds
    const nonCustomFields = this.selectedTextFields
      .filter((f) => !f.isCustomField)
      .map((field) => field.venioFieldId)

    // Extract custom fields and map them to their venioFieldIds
    const customFields = this.selectedTextFields
      .filter((f) => f.isCustomField)
      .map((field) => field.venioFieldId)

    // Return the SearchResultRequestData object
    return {
      pageNumber: 0,
      pageSize: -1,
      viewSession: {
        computedSearchTempTable: this.searchTempTables.computedSearchTempTable,
        searchResultTempTable: this.searchTempTables.searchResultTempTable,
      },
      // Use ternary operators to directly assign values to venioFieldIds and customFieldIds.
      // If the array is empty, default to [-1]
      venioFieldIds: nonCustomFields.length > 0 ? nonCustomFields : [-1],
      customFieldIds: customFields.length > 0 ? customFields : [-1],
      searchFieldViewerType: SearchFieldViewerType.Meta_Detail,
      isExternalUser: false,
      overrideGroupSecurity: false,
    }
  }

  private downloadFulltextFile(): void {
    this.fulltextFacade
      .downloadFulltextFile(this.projectId, this.currentFileId, {
        fulltextType: this.selectedFulltextType,
        imageSetId: -1,
      })
      .pipe(
        catchError((err: unknown) => {
          //err.error is a Blob that we can turn into a Response
          const errResponse = new Response((err as HttpErrorResponse).error)

          // Convert the Promise to an Observable
          return from(errResponse.json()).pipe(
            switchMap((jsonErr) => {
              this.notificationService.showError(jsonErr.message)
              return EMPTY
            }),
            catchError((err: unknown) => {
              return EMPTY
            })
          )
        }),
        take(1)
      )
      .subscribe((response: Blob) => {
        const url = window.URL.createObjectURL(response)
        const a = document.createElement('a')
        a.href = url
        a.download = this.currentFileId + '.txt'
        a.click()
        window.URL.revokeObjectURL(url)
        a.remove()
      })
  }

  public onTextSearchAction(): void {
    this.fulltextFacade.textSearchActionHandler
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((action) => {
        this.textSearchActionHandler(action)
      })
  }

  private textSearchActionHandler(action: ActionModel): void {
    switch (action.actionType) {
      case FulltextAction.Find:
        this.textTermHighlighterProperties.textSearchTerm =
          action.data.searchTerm
        this.textTermHighlighter.highlightSearchTerm(action.data.searchTerm)

        break
      case FulltextAction.Next:
        this.highlightNavigator.navigateTerms(
          HighlightNavigationAction.Next,
          HighlightType.TextTerm
        )
        break
      case FulltextAction.Previous:
        //this.textTermHighlighter.activateHighlight(action.actionType)
        this.highlightNavigator.navigateTerms(
          HighlightNavigationAction.Previous,
          HighlightType.TextTerm
        )

        break
      case FulltextAction.Close:
        this.textTermHighlighterProperties.showTextSearch = false
        this.textTermHighlighter.removePreviouslyMarkedTextInNode()
        break
    }
    this.showNavigationButton =
      this.container.nativeElement.querySelectorAll('mark').length > 0
    this.cdr.markForCheck()
  }

  public highlightSimilarTerms(): void {
    this.fulltextFacade.activateSimilarTermsHighlight$
      .pipe(
        switchMap(() =>
          this.utilityPanelFacade.selectVisibilityStatusByKey$(
            UtilityPanelTitle.SIMILAR_DOCUMENTS
          )
        ),
        filter((result) => Boolean(result)),
        switchMap(() => this.reviewPanelFacade.selectSimilarDocumentTerms$),
        takeUntil(this.toDestroy$)
      )
      .subscribe((similarTerms: string[]) => {
        this.textTermHighlighter.hilightSimilarTerms(similarTerms)
        this.showNavigationButton =
          this.container.nativeElement.querySelectorAll('mark').length > 0
        this.cdr.markForCheck()
      })
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()

    this.shortcutManager?.reset()
  }
}
