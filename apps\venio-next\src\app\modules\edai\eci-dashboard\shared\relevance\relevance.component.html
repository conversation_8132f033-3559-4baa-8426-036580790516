<div
  id="relevance-chart-container"
  class="t-bg-white t-border t-border-[#dcdcdc] t-flex t-gap-6 t-p-7 t-rounded t-flex-col">
  <venio-title-and-download
    [title]="sunburstChartType.Relevance"
    [dashboardType]="dashboardType.Relevance" />
  <div class="t-relative t-h-[300px] md:t-h-[400px] t-overflow-hidden">
    @if (isEcaRelevanceLoading()) {
    <div
      class="t-flex t-items-center t-justify-center t-h-[300px] md:t-h-[400px]">
      <kendo-loader size="large" />
    </div>
    } @else if (ecaRelevanceError()) {
    <div
      class="t-flex t-items-center t-justify-center t-h-[300px] md:t-h-[400px] t-text-red-500">
      <p>Error loading relevance data: {{ ecaRelevanceError()?.message }}</p>
    </div>
    } @else {
    <venio-center-text
      [centerText]="sunburstChartType.Relevance"
      [showViewDetails]="true"
      [isExpanded]="false" />

    @if (shouldRenderChart()) {
    <plotly-plot
      id="relevance-chart"
      [data]="graph().data"
      [layout]="graph().layout"
      [config]="config" />
    } @else {
    <div class="t-flex t-items-center t-justify-center t-h-full">
      <div class="t-text-gray-500">Loading chart...</div>
    </div>
    } }
  </div>
  <div class="t-w-full t-flex t-flex-row t-justify-start t-flex-wrap t-gap-2">
    <ng-container *ngFor="let legend of labels(); let i = index">
      <div class="t-flex t-items-center t-flex-row t-mb-2 t-mr-3">
        <div
          class="t-h-3 t-w-3 t-rounded-sm t-mr-2"
          [style]="{ background: getColor(i) }"></div>
        <p class="t-font-medium t-text-xs">{{ legend }}</p>
      </div>
    </ng-container>
  </div>
</div>
